const axios = require("axios");
const fs = require("fs");
const path = require("path");
const { csvLinks } = require("./csv-links");

const API_ENDPOINT = "https://lumenstaging.kickavenue.com/admins/bulk/prices";

const locationErrorFiles = (sheetName) => {
  return path.join(__dirname, `/error-files/${sheetName}-error.csv`);
};

const checkContentTypeFile = (headers) => {
  const contentType = headers?.["Content-Type"] || headers?.["content-type"];
  if (!contentType) return false;

  return (
    contentType?.includes("text/csv") ||
    contentType?.includes("application/octet-stream") ||
    contentType?.includes(
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
  );
};

async function processSheet({ sheet, url }) {
  try {
    const formData = new URLSearchParams();
    formData.append("link", url);

    const res = await axios.post(API_ENDPOINT, formData, {
      headers: {
        Authorization: `Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DnelXKlgup7s4jUHbB-j2cqJe8cuVAsKCThkRMG59Ew`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
      responseType: "arraybuffer", // penting untuk handle file error
    });

    if (checkContentTypeFile(res?.headers)) {
      console.log("checkContentTypeFile enter success");

      const filePath = locationErrorFiles(sheet);
      // Convert arraybuffer to string with proper encoding
      const csvContent = Buffer.from(res.data).toString("utf-8");
      fs.writeFileSync(filePath, csvContent, "utf-8");
      console.warn(`⚠️  Error response saved as ${sheet}-error.csv`);
    } else {
      console.log(`✅ ${sheet} processed successfully`);
    }
  } catch (err) {
    const res = err.response;

    if (checkContentTypeFile(res?.headers)) {
      console.log("checkContentTypeFile enter error");

      const filePath = locationErrorFiles(sheet);
      // Convert arraybuffer to string with proper encoding
      const csvContent = Buffer.from(res.data).toString("utf-8");
      fs.writeFileSync(filePath, csvContent, "utf-8");
      console.error(
        `❌ ${sheet} failed, error CSV saved as ${sheet}-error.csv`
      );
    } else {
      console.error(`❌ ${sheet} failed`, res?.data || err.message);
    }
  }
}

async function main() {
  const sheets = csvLinks.published;
  for (const entry of sheets) {
    console.log(`\n--- Processing ${entry.sheet} ---`);

    await new Promise((resolve) => setTimeout(resolve, 1000));

    await processSheet(entry);
  }
}

main();
